#!/usr/bin/env python3
"""
详细的全球水电站数据分析报告
基于Nature Water论文补充数据进行深度分析
"""

import os
import sys
import pandas as pd
import geopandas as gpd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_analyze_hydropower_data():
    """加载并分析水电站数据"""
    
    # 数据路径
    shapefile_path = "/Users/<USER>/Downloads/文献附件下载/GlobalHydropower-main/HydroStation-NW/HydroStation/HydroStation.shp"
    
    print("="*80)
    print("全球水电站数据深度分析报告")
    print("数据来源: Nature Water论文 - 徐荣嵘博士补充数据")
    print("="*80)
    
    # 加载数据
    print("\n1. 数据加载...")
    gdf = gpd.read_file(shapefile_path)
    print(f"✓ 成功加载 {len(gdf):,} 个水电站数据点")
    
    # 添加坐标列
    gdf['longitude'] = gdf.geometry.x
    gdf['latitude'] = gdf.geometry.y
    
    # 数据结构分析
    print("\n2. 数据结构分析")
    print("-" * 50)
    print(f"数据维度: {gdf.shape}")
    print(f"坐标系统: {gdf.crs}")
    print(f"几何类型: Point")
    
    print("\n字段详情:")
    for col in gdf.columns:
        if col != 'geometry':
            dtype = str(gdf[col].dtype)
            non_null = gdf[col].count()
            unique_count = gdf[col].nunique()
            print(f"  {col:<12}: {dtype:<10} | 非空: {non_null:>6} | 唯一值: {unique_count:>6}")
    
    # 站点类型分析
    print("\n3. 站点类型分析")
    print("-" * 50)
    class_counts = gdf['Class'].value_counts().sort_index()
    total_stations = len(gdf)
    
    for class_val, count in class_counts.items():
        percentage = (count / total_stations) * 100
        station_type = "大坝式水电站 (reservoir-type)" if class_val == 1 else "引水式水电站 (run-of-river)"
        print(f"  类型 {class_val} - {station_type}")
        print(f"    数量: {count:,} 个站点 ({percentage:.1f}%)")
    
    # LCOE分析
    print("\n4. LCOE (平准化电力成本) 分析")
    print("-" * 50)
    lcoe_data = gdf['LCOE']
    print(f"  数据范围: {lcoe_data.min():.4f} - {lcoe_data.max():.4f} USD/kWh")
    print(f"  平均值: {lcoe_data.mean():.4f} USD/kWh")
    print(f"  中位数: {lcoe_data.median():.4f} USD/kWh")
    print(f"  标准差: {lcoe_data.std():.4f}")
    
    # LCOE分布分析
    lcoe_ranges = [
        (0.0, 0.1, "极低成本"),
        (0.1, 0.2, "低成本"),
        (0.2, 0.3, "中等成本"),
        (0.3, 0.4, "较高成本"),
        (0.4, 0.5, "高成本")
    ]
    
    print("\n  LCOE成本分布:")
    for min_val, max_val, label in lcoe_ranges:
        count = len(gdf[(gdf['LCOE'] >= min_val) & (gdf['LCOE'] < max_val)])
        percentage = (count / total_stations) * 100
        print(f"    {label} ({min_val:.1f}-{max_val:.1f}): {count:,} 个 ({percentage:.1f}%)")
    
    # 发电量分析
    print("\n5. 发电量分析")
    print("-" * 50)
    power_data = gdf['Power']
    total_power_twh = power_data.sum() / 1e12  # 转换为TWh
    
    print(f"  总发电量: {total_power_twh:.1f} TWh/年")
    print(f"  平均发电量: {power_data.mean()/1e6:.1f} GWh/年")
    print(f"  中位数发电量: {power_data.median()/1e6:.1f} GWh/年")
    print(f"  最大发电量: {power_data.max()/1e9:.1f} TWh/年")
    print(f"  最小发电量: {power_data.min()/1e6:.1f} GWh/年")
    
    # 发电量规模分布
    power_ranges = [
        (0, 1e9, "小型 (<1 GWh)"),
        (1e9, 10e9, "中型 (1-10 GWh)"),
        (10e9, 100e9, "大型 (10-100 GWh)"),
        (100e9, float('inf'), "超大型 (>100 GWh)")
    ]
    
    print("\n  发电规模分布:")
    for min_val, max_val, label in power_ranges:
        if max_val == float('inf'):
            count = len(gdf[gdf['Power'] >= min_val])
        else:
            count = len(gdf[(gdf['Power'] >= min_val) & (gdf['Power'] < max_val)])
        percentage = (count / total_stations) * 100
        print(f"    {label}: {count:,} 个 ({percentage:.1f}%)")
    
    # 地理分布分析
    print("\n6. 地理分布分析")
    print("-" * 50)
    
    # 简单的大洲分类
    def classify_region(row):
        lon, lat = row['longitude'], row['latitude']
        if -180 <= lon <= -30:  # 美洲
            if lat >= 15:
                return "北美洲"
            else:
                return "南美洲"
        elif -30 < lon <= 60:  # 欧洲和非洲
            if lat > 35:
                return "欧洲"
            else:
                return "非洲"
        elif 60 < lon <= 150:  # 亚洲
            return "亚洲"
        else:  # 大洋洲
            return "大洋洲"
    
    gdf['region'] = gdf.apply(classify_region, axis=1)
    region_stats = gdf['region'].value_counts()
    
    print("  按大洲分布:")
    for region, count in region_stats.items():
        percentage = (count / total_stations) * 100
        avg_lcoe = gdf[gdf['region'] == region]['LCOE'].mean()
        total_power_region = gdf[gdf['region'] == region]['Power'].sum() / 1e12
        print(f"    {region:<8}: {count:>6,} 个 ({percentage:>5.1f}%) | 平均LCOE: {avg_lcoe:.3f} | 总发电量: {total_power_region:>6.1f} TWh")
    
    # 按站点类型的地理分布
    print("\n7. 按站点类型的地理分布")
    print("-" * 50)
    
    for class_val in sorted(gdf['Class'].unique()):
        station_type = "大坝式" if class_val == 1 else "引水式"
        class_data = gdf[gdf['Class'] == class_val]
        
        print(f"\n  {station_type}水电站 (类型 {class_val}):")
        class_region_stats = class_data['region'].value_counts()
        
        for region, count in class_region_stats.items():
            percentage = (count / len(class_data)) * 100
            avg_lcoe = class_data[class_data['region'] == region]['LCOE'].mean()
            print(f"    {region:<8}: {count:>6,} 个 ({percentage:>5.1f}%) | 平均LCOE: {avg_lcoe:.3f}")
    
    # MILP模型集成评估
    print("\n8. MILP模型集成评估")
    print("-" * 50)
    
    print("  数据完整性检查:")
    required_fields = ['longitude', 'latitude', 'LCOE', 'Power', 'Class']
    for field in required_fields:
        if field in gdf.columns:
            missing_count = gdf[field].isnull().sum()
            print(f"    ✓ {field}: {missing_count} 个缺失值")
        else:
            print(f"    ✗ {field}: 字段不存在")
    
    print("\n  模型参数映射:")
    print("    ✓ 站点坐标 → 空间定位")
    print("    ✓ LCOE → 投资成本参数 (IC_{n,h}^{hydro_res})")
    print("    ✓ Power → 装机潜力上限 (I_{n,h}^{hydro_res,p})")
    print("    ✓ Class → 技术类型区分")
    
    print("\n  数据处理建议:")
    print("    1. 空间聚合: 将站点坐标映射到202个国家")
    print("    2. 参数转换: LCOE转换为投资成本，Power转换为装机容量")
    print("    3. 时序补充: 需要添加月度/季度容量因子数据")
    print("    4. 技术约束: 基于Class字段设置不同的技术参数")
    
    # 数据质量评估
    print("\n9. 数据质量评估")
    print("-" * 50)
    
    quality_score = 0
    max_score = 5
    
    # 检查1: 数据完整性
    if gdf.isnull().sum().sum() == 0:
        print("  ✓ 数据完整性: 无缺失值")
        quality_score += 1
    else:
        print("  ✗ 数据完整性: 存在缺失值")
    
    # 检查2: 坐标有效性
    invalid_coords = len(gdf[(gdf['longitude'] < -180) | (gdf['longitude'] > 180) | 
                            (gdf['latitude'] < -90) | (gdf['latitude'] > 90)])
    if invalid_coords == 0:
        print("  ✓ 坐标有效性: 所有坐标在有效范围内")
        quality_score += 1
    else:
        print(f"  ✗ 坐标有效性: {invalid_coords} 个无效坐标")
    
    # 检查3: LCOE合理性
    reasonable_lcoe = len(gdf[(gdf['LCOE'] >= 0.01) & (gdf['LCOE'] <= 1.0)])
    if reasonable_lcoe == len(gdf):
        print("  ✓ LCOE合理性: 所有值在合理范围内")
        quality_score += 1
    else:
        print(f"  ✗ LCOE合理性: {len(gdf) - reasonable_lcoe} 个异常值")
    
    # 检查4: 发电量合理性
    reasonable_power = len(gdf[gdf['Power'] > 0])
    if reasonable_power == len(gdf):
        print("  ✓ 发电量合理性: 所有值为正数")
        quality_score += 1
    else:
        print(f"  ✗ 发电量合理性: {len(gdf) - reasonable_power} 个非正值")
    
    # 检查5: 重复数据
    duplicates = gdf.duplicated(subset=['longitude', 'latitude']).sum()
    if duplicates == 0:
        print("  ✓ 数据唯一性: 无重复坐标")
        quality_score += 1
    else:
        print(f"  ✗ 数据唯一性: {duplicates} 个重复坐标")
    
    print(f"\n  数据质量评分: {quality_score}/{max_score} ({quality_score/max_score*100:.0f}%)")
    
    # 保存分析结果
    analysis_results = {
        'metadata': {
            'analysis_date': datetime.now().isoformat(),
            'total_stations': len(gdf),
            'data_source': 'Nature Water - Xu et al. 2023'
        },
        'data_structure': {
            'shape': gdf.shape,
            'crs': str(gdf.crs),
            'columns': list(gdf.columns)
        },
        'station_types': {
            'reservoir_type': int(class_counts.get(1, 0)),
            'run_of_river': int(class_counts.get(2, 0))
        },
        'lcoe_statistics': {
            'mean': float(lcoe_data.mean()),
            'median': float(lcoe_data.median()),
            'std': float(lcoe_data.std()),
            'min': float(lcoe_data.min()),
            'max': float(lcoe_data.max())
        },
        'power_statistics': {
            'total_twh': float(total_power_twh),
            'mean_gwh': float(power_data.mean() / 1e9),
            'median_gwh': float(power_data.median() / 1e9),
            'max_twh': float(power_data.max() / 1e12)
        },
        'regional_distribution': region_stats.to_dict(),
        'data_quality_score': f"{quality_score}/{max_score}"
    }
    
    # 保存结果到JSON文件
    with open('hydropower_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n10. 分析完成")
    print("-" * 50)
    print("  ✓ 分析结果已保存到: hydropower_analysis_results.json")
    print(f"  ✓ 数据集包含 {len(gdf):,} 个全球水电站")
    print(f"  ✓ 总发电潜力: {total_power_twh:.1f} TWh/年")
    print(f"  ✓ 平均LCOE: {lcoe_data.mean():.3f} USD/kWh")
    
    return gdf, analysis_results

if __name__ == "__main__":
    gdf, results = load_and_analyze_hydropower_data()
