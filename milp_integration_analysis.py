#!/usr/bin/env python3
"""
MILP模型集成分析
评估水电站数据与全球电力系统扩展规划MILP模型的集成方案
"""

import pandas as pd
import geopandas as gpd
import numpy as np
import json
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

def analyze_milp_integration():
    """分析MILP模型集成方案"""
    
    print("="*80)
    print("全球水电站数据与MILP模型集成分析")
    print("="*80)
    
    # 加载数据
    shapefile_path = "/Users/<USER>/Downloads/文献附件下载/GlobalHydropower-main/HydroStation-NW/HydroStation/HydroStation.shp"
    gdf = gpd.read_file(shapefile_path)
    
    # 添加坐标
    gdf['longitude'] = gdf.geometry.x
    gdf['latitude'] = gdf.geometry.y
    
    print(f"数据集概况: {len(gdf):,} 个水电站")
    
    # 1. 数据字段映射分析
    print("\n1. MILP模型参数映射分析")
    print("-" * 50)
    
    milp_mapping = {
        'H_n': {
            'description': '国家n的水电站集合',
            'current_data': '站点坐标 (longitude, latitude)',
            'mapping_method': '基于坐标进行国家边界匹配',
            'availability': '✓ 可用'
        },
        'IC_hydro_res': {
            'description': '水电站投资成本 ($/MW)',
            'current_data': 'LCOE ($/kWh)',
            'mapping_method': '需要转换公式: IC = LCOE × 年发电小时数 × 容量因子',
            'availability': '⚠️ 需要转换'
        },
        'MC_hydro_res': {
            'description': '水电站运行边际成本 ($/MWh)',
            'current_data': '无直接数据',
            'mapping_method': '可基于LCOE估算，通常为0-5 $/MWh',
            'availability': '⚠️ 需要估算'
        },
        'I_hydro_res_p': {
            'description': '水电站装机潜力上限 (MW)',
            'current_data': 'Power (kWh年发电量)',
            'mapping_method': '转换公式: 装机容量 = 年发电量 / (8760 × 容量因子)',
            'availability': '⚠️ 需要转换'
        },
        'alpha_hydro_res': {
            'description': '水电站容量因子 (月度/季度)',
            'current_data': '无时序数据',
            'mapping_method': '需要补充流域水文数据或使用典型值',
            'availability': '✗ 缺失'
        },
        'tech_type': {
            'description': '技术类型标识',
            'current_data': 'Class (1=大坝式, 2=引水式)',
            'mapping_method': '直接映射',
            'availability': '✓ 可用'
        }
    }
    
    for param, info in milp_mapping.items():
        print(f"\n{param}:")
        print(f"  描述: {info['description']}")
        print(f"  当前数据: {info['current_data']}")
        print(f"  映射方法: {info['mapping_method']}")
        print(f"  可用性: {info['availability']}")
    
    # 2. 国家级聚合分析
    print("\n\n2. 国家级数据聚合分析")
    print("-" * 50)
    
    # 简化的国家分类（基于坐标范围）
    def classify_country_region(row):
        lon, lat = row['longitude'], row['latitude']
        
        # 主要国家/地区的简化分类
        if -130 <= lon <= -60 and 25 <= lat <= 50:
            return "USA"
        elif -80 <= lon <= -35 and -35 <= lat <= 15:
            return "Brazil"
        elif 70 <= lon <= 140 and 15 <= lat <= 55:
            return "China"
        elif 35 <= lon <= 45 and 55 <= lat <= 70:
            return "Russia"
        elif -10 <= lon <= 30 and 35 <= lat <= 70:
            return "Europe"
        elif 65 <= lon <= 100 and 5 <= lat <= 40:
            return "India"
        elif 110 <= lon <= 155 and -45 <= lat <= -10:
            return "Australia"
        elif -85 <= lon <= -65 and -25 <= lat <= 15:
            return "South_America_Other"
        elif 15 <= lon <= 55 and -35 <= lat <= 35:
            return "Africa"
        elif 90 <= lon <= 150 and -10 <= lat <= 25:
            return "Southeast_Asia"
        else:
            return "Other"
    
    gdf['country_region'] = gdf.apply(classify_country_region, axis=1)
    
    # 按国家/地区聚合统计
    country_stats = gdf.groupby('country_region').agg({
        'Class': 'count',  # 站点数量
        'LCOE': ['mean', 'std'],  # LCOE统计
        'Power': ['sum', 'mean'],  # 发电量统计
        'longitude': 'mean',  # 中心坐标
        'latitude': 'mean'
    }).round(4)
    
    country_stats.columns = ['站点数量', 'LCOE均值', 'LCOE标准差', '总发电量', '平均发电量', '中心经度', '中心纬度']
    
    print("主要国家/地区水电站统计:")
    print(country_stats.sort_values('站点数量', ascending=False))
    
    # 3. 技术类型分析
    print("\n\n3. 技术类型特征分析")
    print("-" * 50)
    
    tech_analysis = gdf.groupby('Class').agg({
        'LCOE': ['count', 'mean', 'std', 'min', 'max'],
        'Power': ['sum', 'mean', 'std']
    }).round(4)
    
    tech_analysis.columns = ['数量', 'LCOE均值', 'LCOE标准差', 'LCOE最小', 'LCOE最大', 
                            '总发电量', '平均发电量', '发电量标准差']
    
    tech_names = {1: '大坝式水电站', 2: '引水式水电站'}
    tech_analysis.index = [tech_names[i] for i in tech_analysis.index]
    
    print("按技术类型统计:")
    print(tech_analysis)
    
    # 4. 参数转换示例
    print("\n\n4. MILP模型参数转换示例")
    print("-" * 50)
    
    # 假设参数
    typical_capacity_factor = 0.45  # 典型水电容量因子
    annual_hours = 8760
    
    # 计算装机容量 (MW)
    gdf['capacity_mw'] = gdf['Power'] / (annual_hours * typical_capacity_factor * 1000)  # 转换为MW
    
    # 计算投资成本 ($/MW)
    # 假设项目寿命30年，折现率7%
    project_life = 30
    discount_rate = 0.07
    annuity_factor = (discount_rate * (1 + discount_rate)**project_life) / ((1 + discount_rate)**project_life - 1)
    
    gdf['investment_cost_per_mw'] = gdf['LCOE'] * annual_hours * typical_capacity_factor * 1000 / annuity_factor
    
    print("参数转换结果统计:")
    print(f"装机容量范围: {gdf['capacity_mw'].min():.2f} - {gdf['capacity_mw'].max():.2f} MW")
    print(f"平均装机容量: {gdf['capacity_mw'].mean():.2f} MW")
    print(f"总装机容量: {gdf['capacity_mw'].sum():.0f} MW")
    print(f"投资成本范围: {gdf['investment_cost_per_mw'].min():.0f} - {gdf['investment_cost_per_mw'].max():.0f} $/MW")
    print(f"平均投资成本: {gdf['investment_cost_per_mw'].mean():.0f} $/MW")
    
    # 5. 数据完整性评估
    print("\n\n5. MILP模型集成完整性评估")
    print("-" * 50)
    
    required_parameters = [
        ('站点位置', True, '经纬度坐标完整'),
        ('技术类型', True, 'Class字段完整'),
        ('成本数据', True, 'LCOE数据完整'),
        ('容量数据', True, 'Power数据完整，需转换为装机容量'),
        ('时序数据', False, '缺少月度/季度容量因子'),
        ('运行成本', False, '需要基于LCOE估算边际成本'),
        ('技术约束', False, '需要补充技术参数（最小负荷、爬坡率等）')
    ]
    
    available_count = sum(1 for _, available, _ in required_parameters if available)
    total_count = len(required_parameters)
    
    print("参数可用性检查:")
    for param, available, note in required_parameters:
        status = "✓" if available else "✗"
        print(f"  {status} {param}: {note}")
    
    completeness_score = (available_count / total_count) * 100
    print(f"\n数据完整性评分: {completeness_score:.1f}% ({available_count}/{total_count})")
    
    # 6. 集成建议
    print("\n\n6. MILP模型集成建议")
    print("-" * 50)
    
    integration_recommendations = [
        "1. 空间数据处理:",
        "   - 使用国家边界shapefile进行空间连接",
        "   - 将124,761个站点聚合到202个国家",
        "   - 保留站点级详细信息用于精细化分析",
        "",
        "2. 参数转换:",
        "   - LCOE → 投资成本: 使用年金化方法",
        "   - Power → 装机容量: 基于典型容量因子转换",
        "   - 设置典型运行边际成本: 2-5 $/MWh",
        "",
        "3. 时序数据补充:",
        "   - 收集流域水文数据",
        "   - 使用历史发电数据校准容量因子",
        "   - 建立月度/季度变化模式",
        "",
        "4. 技术约束设置:",
        "   - 大坝式: 较高调节能力，容量因子0.4-0.6",
        "   - 引水式: 跟随径流变化，容量因子0.3-0.5",
        "   - 设置合理的技术寿命: 50-100年",
        "",
        "5. 模型验证:",
        "   - 与现有水电装机数据对比",
        "   - 验证成本参数的合理性",
        "   - 检查地理分布的一致性"
    ]
    
    for recommendation in integration_recommendations:
        print(recommendation)
    
    # 7. 保存集成分析结果
    integration_results = {
        'metadata': {
            'total_stations': len(gdf),
            'countries_regions': len(gdf['country_region'].unique()),
            'completeness_score': completeness_score
        },
        'parameter_mapping': milp_mapping,
        'country_statistics': country_stats.to_dict(),
        'technology_analysis': tech_analysis.to_dict(),
        'conversion_examples': {
            'total_capacity_mw': float(gdf['capacity_mw'].sum()),
            'average_investment_cost': float(gdf['investment_cost_per_mw'].mean()),
            'capacity_factor_assumed': typical_capacity_factor
        },
        'integration_readiness': {
            'available_parameters': available_count,
            'total_parameters': total_count,
            'missing_parameters': [param for param, available, _ in required_parameters if not available]
        }
    }
    
    with open('milp_integration_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(integration_results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n\n7. 分析完成")
    print("-" * 50)
    print("✓ 集成分析结果已保存到: milp_integration_analysis.json")
    print(f"✓ 数据集成完整性: {completeness_score:.1f}%")
    print(f"✓ 可直接使用参数: {available_count}/{total_count}")
    print("✓ 建议优先补充时序容量因子数据")
    
    return integration_results

if __name__ == "__main__":
    results = analyze_milp_integration()
