#!/usr/bin/env python3
"""
创建全球水电站数据可视化图表
"""

import os
import pandas as pd
import geopandas as gpd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 设置样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def create_hydropower_visualizations():
    """创建水电站数据可视化"""
    
    # 加载数据
    shapefile_path = "/Users/<USER>/Downloads/文献附件下载/GlobalHydropower-main/HydroStation-NW/HydroStation/HydroStation.shp"
    gdf = gpd.read_file(shapefile_path)
    
    # 添加坐标和区域信息
    gdf['longitude'] = gdf.geometry.x
    gdf['latitude'] = gdf.geometry.y
    
    def classify_region(row):
        lon, lat = row['longitude'], row['latitude']
        if -180 <= lon <= -30:
            if lat >= 15:
                return "北美洲"
            else:
                return "南美洲"
        elif -30 < lon <= 60:
            if lat > 35:
                return "欧洲"
            else:
                return "非洲"
        elif 60 < lon <= 150:
            return "亚洲"
        else:
            return "大洋洲"
    
    gdf['region'] = gdf.apply(classify_region, axis=1)
    gdf['station_type'] = gdf['Class'].map({1: '大坝式', 2: '引水式'})
    
    # 创建输出目录
    os.makedirs('hydropower_visualizations', exist_ok=True)
    
    print("正在生成可视化图表...")
    
    # 1. 全球分布地图
    print("1. 生成全球分布地图...")
    
    # 采样数据以提高性能（如果数据量太大）
    if len(gdf) > 10000:
        sample_gdf = gdf.sample(n=10000, random_state=42)
    else:
        sample_gdf = gdf
    
    fig_map = px.scatter_mapbox(
        sample_gdf,
        lat='latitude',
        lon='longitude',
        color='station_type',
        size='Power',
        hover_data=['LCOE', 'region'],
        title='全球水电站分布图 (采样显示)',
        mapbox_style='open-street-map',
        height=600,
        zoom=1,
        color_discrete_map={'大坝式': '#FF6B6B', '引水式': '#4ECDC4'}
    )
    fig_map.update_layout(
        title_font_size=16,
        font=dict(size=12)
    )
    fig_map.write_html('hydropower_visualizations/global_distribution_map.html')
    
    # 2. 区域分布饼图
    print("2. 生成区域分布图...")
    region_counts = gdf['region'].value_counts()
    
    fig_pie = px.pie(
        values=region_counts.values,
        names=region_counts.index,
        title='全球水电站按区域分布',
        color_discrete_sequence=px.colors.qualitative.Set3
    )
    fig_pie.update_traces(textposition='inside', textinfo='percent+label')
    fig_pie.update_layout(title_font_size=16, font=dict(size=12))
    fig_pie.write_html('hydropower_visualizations/regional_distribution.html')
    
    # 3. 站点类型分布
    print("3. 生成站点类型分布图...")
    type_counts = gdf['station_type'].value_counts()
    
    fig_type = px.bar(
        x=type_counts.index,
        y=type_counts.values,
        title='水电站类型分布',
        labels={'x': '站点类型', 'y': '数量'},
        color=type_counts.index,
        color_discrete_map={'大坝式': '#FF6B6B', '引水式': '#4ECDC4'}
    )
    fig_type.update_layout(title_font_size=16, font=dict(size=12), showlegend=False)
    fig_type.write_html('hydropower_visualizations/station_type_distribution.html')
    
    # 4. LCOE分布直方图
    print("4. 生成LCOE分布图...")
    fig_lcoe = px.histogram(
        gdf,
        x='LCOE',
        nbins=50,
        title='LCOE (平准化电力成本) 分布',
        labels={'LCOE': 'LCOE (USD/kWh)', 'count': '频数'},
        color_discrete_sequence=['#FF9999']
    )
    fig_lcoe.add_vline(x=gdf['LCOE'].mean(), line_dash="dash", line_color="red", 
                       annotation_text=f"平均值: {gdf['LCOE'].mean():.3f}")
    fig_lcoe.add_vline(x=gdf['LCOE'].median(), line_dash="dash", line_color="blue", 
                       annotation_text=f"中位数: {gdf['LCOE'].median():.3f}")
    fig_lcoe.update_layout(title_font_size=16, font=dict(size=12))
    fig_lcoe.write_html('hydropower_visualizations/lcoe_distribution.html')
    
    # 5. 发电量分布（对数尺度）
    print("5. 生成发电量分布图...")
    fig_power = px.histogram(
        gdf,
        x=np.log10(gdf['Power']),
        nbins=50,
        title='发电量分布 (对数尺度)',
        labels={'x': 'log10(发电量 kWh)', 'count': '频数'},
        color_discrete_sequence=['#99FF99']
    )
    fig_power.update_layout(title_font_size=16, font=dict(size=12))
    fig_power.write_html('hydropower_visualizations/power_distribution.html')
    
    # 6. 区域vs站点类型热力图
    print("6. 生成区域-类型交叉分析图...")
    cross_tab = pd.crosstab(gdf['region'], gdf['station_type'])
    
    fig_heatmap = px.imshow(
        cross_tab.values,
        x=cross_tab.columns,
        y=cross_tab.index,
        title='各区域水电站类型分布热力图',
        labels=dict(x="站点类型", y="区域", color="数量"),
        color_continuous_scale='Blues',
        text_auto=True
    )
    fig_heatmap.update_layout(title_font_size=16, font=dict(size=12))
    fig_heatmap.write_html('hydropower_visualizations/region_type_heatmap.html')
    
    # 7. LCOE vs 发电量散点图
    print("7. 生成LCOE vs 发电量关系图...")
    
    # 采样以提高性能
    sample_data = gdf.sample(n=min(5000, len(gdf)), random_state=42)
    
    fig_scatter = px.scatter(
        sample_data,
        x='LCOE',
        y=np.log10(sample_data['Power']),
        color='station_type',
        title='LCOE vs 发电量关系 (采样数据)',
        labels={'LCOE': 'LCOE (USD/kWh)', 'y': 'log10(发电量 kWh)'},
        color_discrete_map={'大坝式': '#FF6B6B', '引水式': '#4ECDC4'},
        opacity=0.6
    )
    fig_scatter.update_layout(title_font_size=16, font=dict(size=12))
    fig_scatter.write_html('hydropower_visualizations/lcoe_vs_power.html')
    
    # 8. 各区域LCOE箱线图
    print("8. 生成各区域LCOE分布箱线图...")
    fig_box = px.box(
        gdf,
        x='region',
        y='LCOE',
        title='各区域LCOE分布',
        labels={'region': '区域', 'LCOE': 'LCOE (USD/kWh)'},
        color='region'
    )
    fig_box.update_layout(title_font_size=16, font=dict(size=12), showlegend=False)
    fig_box.update_xaxes(tickangle=45)
    fig_box.write_html('hydropower_visualizations/regional_lcoe_boxplot.html')
    
    # 9. 发电量累积分布
    print("9. 生成发电量累积分布图...")
    sorted_power = np.sort(gdf['Power'])
    cumulative_power = np.cumsum(sorted_power)
    cumulative_percent = np.arange(1, len(sorted_power) + 1) / len(sorted_power) * 100
    
    fig_cumulative = go.Figure()
    fig_cumulative.add_trace(go.Scatter(
        x=cumulative_percent,
        y=cumulative_power / 1e12,  # 转换为TWh
        mode='lines',
        name='累积发电量',
        line=dict(color='#FF6B6B', width=2)
    ))
    fig_cumulative.update_layout(
        title='全球水电站发电量累积分布',
        xaxis_title='站点百分比 (%)',
        yaxis_title='累积发电量 (TWh)',
        title_font_size=16,
        font=dict(size=12)
    )
    fig_cumulative.write_html('hydropower_visualizations/cumulative_power_distribution.html')
    
    # 10. 综合仪表板
    print("10. 生成综合仪表板...")
    
    # 创建子图
    fig_dashboard = make_subplots(
        rows=2, cols=2,
        subplot_titles=('区域分布', 'LCOE分布', '站点类型', '发电量vs LCOE'),
        specs=[[{"type": "pie"}, {"type": "histogram"}],
               [{"type": "bar"}, {"type": "scatter"}]]
    )
    
    # 区域分布饼图
    fig_dashboard.add_trace(
        go.Pie(labels=region_counts.index, values=region_counts.values, name="区域"),
        row=1, col=1
    )
    
    # LCOE直方图
    fig_dashboard.add_trace(
        go.Histogram(x=gdf['LCOE'], nbinsx=30, name="LCOE"),
        row=1, col=2
    )
    
    # 站点类型柱状图
    fig_dashboard.add_trace(
        go.Bar(x=type_counts.index, y=type_counts.values, name="类型"),
        row=2, col=1
    )
    
    # 散点图
    sample_for_dashboard = gdf.sample(n=1000, random_state=42)
    fig_dashboard.add_trace(
        go.Scatter(
            x=sample_for_dashboard['LCOE'],
            y=sample_for_dashboard['Power'],
            mode='markers',
            name="LCOE vs 发电量",
            marker=dict(size=4, opacity=0.6)
        ),
        row=2, col=2
    )
    
    fig_dashboard.update_layout(
        title_text="全球水电站数据综合仪表板",
        title_font_size=20,
        font=dict(size=10),
        height=800
    )
    fig_dashboard.write_html('hydropower_visualizations/comprehensive_dashboard.html')
    
    print("\n可视化图表生成完成！")
    print("文件保存在 'hydropower_visualizations' 目录中:")
    print("  1. global_distribution_map.html - 全球分布地图")
    print("  2. regional_distribution.html - 区域分布饼图")
    print("  3. station_type_distribution.html - 站点类型分布")
    print("  4. lcoe_distribution.html - LCOE分布直方图")
    print("  5. power_distribution.html - 发电量分布")
    print("  6. region_type_heatmap.html - 区域-类型热力图")
    print("  7. lcoe_vs_power.html - LCOE vs 发电量关系")
    print("  8. regional_lcoe_boxplot.html - 各区域LCOE箱线图")
    print("  9. cumulative_power_distribution.html - 发电量累积分布")
    print("  10. comprehensive_dashboard.html - 综合仪表板")

if __name__ == "__main__":
    create_hydropower_visualizations()
