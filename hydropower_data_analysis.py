#!/usr/bin/env python3
"""
全球水电站数据分析脚本
分析Nature Water论文补充数据：HydroStation-NW shapefile
"""

import os
import pandas as pd
import geopandas as gpd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HydropowerDataAnalyzer:
    def __init__(self, shapefile_path):
        """初始化分析器"""
        self.shapefile_path = shapefile_path
        self.gdf = None
        self.analysis_results = {}
        
    def load_data(self):
        """加载shapefile数据"""
        print("正在加载shapefile数据...")
        try:
            self.gdf = gpd.read_file(self.shapefile_path)
            print(f"成功加载数据，共 {len(self.gdf)} 个水电站")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def analyze_data_structure(self):
        """分析数据结构"""
        print("\n" + "="*50)
        print("1. 数据结构分析")
        print("="*50)
        
        # 基本信息
        print(f"数据形状: {self.gdf.shape}")
        print(f"坐标系统: {self.gdf.crs}")
        print(f"几何类型: {self.gdf.geometry.geom_type.unique()}")
        
        # 字段信息
        print("\n字段详细信息:")
        print("-" * 30)
        for col in self.gdf.columns:
            if col != 'geometry':
                dtype = self.gdf[col].dtype
                non_null = self.gdf[col].count()
                null_count = len(self.gdf) - non_null
                unique_count = self.gdf[col].nunique()
                
                print(f"{col:15} | 类型: {dtype:10} | 非空: {non_null:8} | 空值: {null_count:6} | 唯一值: {unique_count:8}")
                
                # 显示前几个值的示例
                sample_values = self.gdf[col].dropna().head(3).tolist()
                print(f"                | 示例值: {sample_values}")
                print()
        
        # 保存结构信息
        self.analysis_results['data_structure'] = {
            'total_stations': len(self.gdf),
            'columns': list(self.gdf.columns),
            'crs': str(self.gdf.crs),
            'geometry_types': self.gdf.geometry.geom_type.value_counts().to_dict()
        }
    
    def analyze_spatial_distribution(self):
        """分析空间分布"""
        print("\n" + "="*50)
        print("2. 空间分布分析")
        print("="*50)
        
        # 提取坐标
        self.gdf['longitude'] = self.gdf.geometry.x
        self.gdf['latitude'] = self.gdf.geometry.y
        
        # 基本统计
        print("地理坐标统计:")
        print(f"经度范围: {self.gdf['longitude'].min():.2f} 到 {self.gdf['longitude'].max():.2f}")
        print(f"纬度范围: {self.gdf['latitude'].min():.2f} 到 {self.gdf['latitude'].max():.2f}")
        
        # 按大洲/区域分布（简单的经纬度划分）
        def classify_region(row):
            lon, lat = row['longitude'], row['latitude']
            if -180 <= lon <= -30:
                if lat >= 15:
                    return "北美洲"
                else:
                    return "南美洲"
            elif -30 < lon <= 60:
                if lat >= 35:
                    return "欧洲"
                elif lat >= -35:
                    return "非洲"
                else:
                    return "南极洲"
            else:  # 60 < lon <= 180
                if lat >= 15:
                    return "亚洲"
                else:
                    return "大洋洲"
        
        self.gdf['region'] = self.gdf.apply(classify_region, axis=1)
        
        region_stats = self.gdf['region'].value_counts()
        print("\n按区域分布:")
        for region, count in region_stats.items():
            percentage = (count / len(self.gdf)) * 100
            print(f"{region:8}: {count:6} 个站点 ({percentage:5.1f}%)")
        
        self.analysis_results['spatial_distribution'] = {
            'longitude_range': [self.gdf['longitude'].min(), self.gdf['longitude'].max()],
            'latitude_range': [self.gdf['latitude'].min(), self.gdf['latitude'].max()],
            'region_distribution': region_stats.to_dict()
        }
    
    def analyze_technical_parameters(self):
        """分析技术参数"""
        print("\n" + "="*50)
        print("3. 技术参数统计分析")
        print("="*50)
        
        # 站点类型分析
        if 'Type' in self.gdf.columns:
            type_mapping = {1: '大坝式(reservoir-type)', 2: '引水式(run-of-river)'}
            self.gdf['station_type'] = self.gdf['Type'].map(type_mapping)
            
            type_stats = self.gdf['station_type'].value_counts()
            print("站点类型分布:")
            for stype, count in type_stats.items():
                percentage = (count / len(self.gdf)) * 100
                print(f"{stype:20}: {count:6} 个 ({percentage:5.1f}%)")
        
        # LCOE分析
        if 'LCOE' in self.gdf.columns:
            lcoe_data = self.gdf['LCOE'].dropna()
            print(f"\nLCOE (平准化电力成本) 统计 (USD/kWh):")
            print(f"样本数量: {len(lcoe_data)}")
            print(f"平均值: {lcoe_data.mean():.4f}")
            print(f"中位数: {lcoe_data.median():.4f}")
            print(f"标准差: {lcoe_data.std():.4f}")
            print(f"最小值: {lcoe_data.min():.4f}")
            print(f"最大值: {lcoe_data.max():.4f}")
            
            # 分位数
            percentiles = [10, 25, 50, 75, 90, 95, 99]
            print("分位数分布:")
            for p in percentiles:
                value = np.percentile(lcoe_data, p)
                print(f"  {p:2d}%: {value:.4f}")
        
        # Power分析
        if 'Power' in self.gdf.columns:
            power_data = self.gdf['Power'].dropna()
            print(f"\n年均发电量统计 (kWh):")
            print(f"样本数量: {len(power_data)}")
            print(f"总发电量: {power_data.sum():,.0f} kWh")
            print(f"平均值: {power_data.mean():,.0f}")
            print(f"中位数: {power_data.median():,.0f}")
            print(f"标准差: {power_data.std():,.0f}")
            print(f"最小值: {power_data.min():,.0f}")
            print(f"最大值: {power_data.max():,.0f}")
            
            # 按规模分类
            def classify_power_scale(power):
                if power < 1e6:  # < 1 GWh
                    return "小型 (<1 GWh)"
                elif power < 1e7:  # < 10 GWh
                    return "中型 (1-10 GWh)"
                elif power < 1e8:  # < 100 GWh
                    return "大型 (10-100 GWh)"
                else:
                    return "超大型 (>100 GWh)"
            
            self.gdf['power_scale'] = self.gdf['Power'].apply(classify_power_scale)
            scale_stats = self.gdf['power_scale'].value_counts()
            print("\n按发电规模分布:")
            for scale, count in scale_stats.items():
                percentage = (count / len(self.gdf)) * 100
                print(f"{scale:15}: {count:6} 个 ({percentage:5.1f}%)")
        
        # 保存技术参数统计
        self.analysis_results['technical_parameters'] = {
            'station_types': type_stats.to_dict() if 'Type' in self.gdf.columns else {},
            'lcoe_stats': {
                'count': len(lcoe_data),
                'mean': lcoe_data.mean(),
                'median': lcoe_data.median(),
                'std': lcoe_data.std(),
                'min': lcoe_data.min(),
                'max': lcoe_data.max()
            } if 'LCOE' in self.gdf.columns else {},
            'power_stats': {
                'count': len(power_data),
                'total': power_data.sum(),
                'mean': power_data.mean(),
                'median': power_data.median(),
                'std': power_data.std(),
                'min': power_data.min(),
                'max': power_data.max()
            } if 'Power' in self.gdf.columns else {}
        }
    
    def assess_data_quality(self):
        """评估数据质量"""
        print("\n" + "="*50)
        print("4. 数据质量评估")
        print("="*50)
        
        quality_issues = []
        
        # 检查空值
        print("空值检查:")
        for col in self.gdf.columns:
            if col != 'geometry':
                null_count = self.gdf[col].isnull().sum()
                null_percentage = (null_count / len(self.gdf)) * 100
                print(f"{col:15}: {null_count:6} 个空值 ({null_percentage:5.1f}%)")
                
                if null_percentage > 10:
                    quality_issues.append(f"{col}字段空值率过高: {null_percentage:.1f}%")
        
        # 检查异常值
        print("\n异常值检查:")
        
        if 'LCOE' in self.gdf.columns:
            lcoe_data = self.gdf['LCOE'].dropna()
            # 使用IQR方法检测异常值
            Q1 = lcoe_data.quantile(0.25)
            Q3 = lcoe_data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = lcoe_data[(lcoe_data < lower_bound) | (lcoe_data > upper_bound)]
            print(f"LCOE异常值: {len(outliers)} 个 ({len(outliers)/len(lcoe_data)*100:.1f}%)")
            
            if len(outliers) > 0:
                print(f"  异常值范围: {outliers.min():.4f} - {outliers.max():.4f}")
                if len(outliers) / len(lcoe_data) > 0.05:
                    quality_issues.append(f"LCOE异常值比例过高: {len(outliers)/len(lcoe_data)*100:.1f}%")
        
        if 'Power' in self.gdf.columns:
            power_data = self.gdf['Power'].dropna()
            # 检查负值或零值
            invalid_power = power_data[power_data <= 0]
            print(f"发电量无效值: {len(invalid_power)} 个 ({len(invalid_power)/len(power_data)*100:.1f}%)")
            
            if len(invalid_power) > 0:
                quality_issues.append(f"发电量存在无效值: {len(invalid_power)} 个")
        
        # 检查坐标有效性
        invalid_coords = self.gdf[
            (self.gdf['longitude'] < -180) | (self.gdf['longitude'] > 180) |
            (self.gdf['latitude'] < -90) | (self.gdf['latitude'] > 90)
        ]
        print(f"无效坐标: {len(invalid_coords)} 个")
        
        if len(invalid_coords) > 0:
            quality_issues.append(f"存在无效坐标: {len(invalid_coords)} 个")
        
        # 检查重复记录
        duplicate_coords = self.gdf.duplicated(subset=['longitude', 'latitude'])
        duplicate_count = duplicate_coords.sum()
        print(f"重复坐标: {duplicate_count} 个 ({duplicate_count/len(self.gdf)*100:.1f}%)")
        
        if duplicate_count > 0:
            quality_issues.append(f"存在重复坐标: {duplicate_count} 个")
        
        print(f"\n数据质量总结:")
        if quality_issues:
            print("发现的质量问题:")
            for issue in quality_issues:
                print(f"  - {issue}")
        else:
            print("未发现明显的数据质量问题")
        
        self.analysis_results['data_quality'] = {
            'quality_issues': quality_issues,
            'null_percentages': {col: (self.gdf[col].isnull().sum() / len(self.gdf)) * 100 
                               for col in self.gdf.columns if col != 'geometry'},
            'duplicate_coordinates': duplicate_count
        }
    
    def evaluate_milp_integration(self):
        """评估与MILP模型的集成可能性"""
        print("\n" + "="*50)
        print("5. MILP模型集成评估")
        print("="*50)
        
        # 检查必需的字段
        required_fields = {
            'station_id': '站点唯一标识符',
            'coordinates': '地理坐标',
            'station_type': '站点类型(水库式/径流式)',
            'capacity': '装机容量',
            'lcoe': '平准化电力成本',
            'power_generation': '发电量'
        }
        
        available_fields = {}
        missing_fields = []
        
        # 检查坐标
        if 'longitude' in self.gdf.columns and 'latitude' in self.gdf.columns:
            available_fields['coordinates'] = '✓ 经纬度坐标可用'
        else:
            missing_fields.append('地理坐标')
        
        # 检查站点类型
        if 'Type' in self.gdf.columns:
            available_fields['station_type'] = '✓ 站点类型可用 (1=大坝式, 2=引水式)'
        else:
            missing_fields.append('站点类型')
        
        # 检查LCOE
        if 'LCOE' in self.gdf.columns:
            available_fields['lcoe'] = '✓ LCOE数据可用'
        else:
            missing_fields.append('LCOE数据')
        
        # 检查发电量
        if 'Power' in self.gdf.columns:
            available_fields['power_generation'] = '✓ 年均发电量可用'
        else:
            missing_fields.append('发电量数据')
        
        # 检查站点ID
        potential_id_fields = [col for col in self.gdf.columns if 'id' in col.lower() or 'fid' in col.lower()]
        if potential_id_fields:
            available_fields['station_id'] = f'✓ 可能的ID字段: {potential_id_fields}'
        else:
            missing_fields.append('站点唯一标识符')
        
        print("MILP模型所需数据字段检查:")
        print("可用字段:")
        for field, description in available_fields.items():
            print(f"  {description}")
        
        if missing_fields:
            print("\n缺失字段:")
            for field in missing_fields:
                print(f"  - {field}")
        
        # 数据转换建议
        print("\n数据转换建议:")
        print("1. 站点级建模支持:")
        print(f"   - 当前数据包含 {len(self.gdf)} 个站点，支持站点级精细化建模")
        print("   - 可直接用于MILP模型中的 H_n 集合(国家n的水电站集合)")
        
        print("\n2. 参数映射:")
        if 'LCOE' in self.gdf.columns:
            print("   - LCOE → IC_{n,h}^{hydro_res} (投资成本参数)")
        if 'Power' in self.gdf.columns:
            print("   - Power → I_{n,h}^{hydro_res,p} (装机潜力上限)")
        if 'Type' in self.gdf.columns:
            print("   - Type → 区分水库式和径流式建模")
        
        print("\n3. 空间聚合:")
        print("   - 需要将站点坐标映射到202个国家")
        print("   - 建议使用国家边界数据进行空间连接")
        
        print("\n4. 时序数据补充:")
        print("   - 当前缺少月度/季度容量因子数据")
        print("   - 建议结合流域水文数据生成时序参数")
        
        # 计算集成可行性评分
        total_required = len(required_fields)
        available_count = len(available_fields)
        integration_score = (available_count / total_required) * 100
        
        print(f"\n集成可行性评分: {integration_score:.1f}% ({available_count}/{total_required} 个必需字段可用)")
        
        self.analysis_results['milp_integration'] = {
            'available_fields': available_fields,
            'missing_fields': missing_fields,
            'integration_score': integration_score,
            'recommendations': [
                "使用国家边界进行空间聚合",
                "补充时序容量因子数据",
                "建立站点ID到MILP模型参数的映射",
                "验证LCOE和发电量数据的单位一致性"
            ]
        }

def main():
    """主函数"""
    shapefile_path = "/Users/<USER>/Downloads/文献附件下载/GlobalHydropower-main/HydroStation-NW/HydroStation/HydroStation.shp"
    
    # 创建分析器
    analyzer = HydropowerDataAnalyzer(shapefile_path)
    
    # 加载数据
    if not analyzer.load_data():
        return
    
    # 执行各项分析
    analyzer.analyze_data_structure()
    analyzer.analyze_spatial_distribution()
    analyzer.analyze_technical_parameters()
    analyzer.assess_data_quality()
    analyzer.evaluate_milp_integration()
    
    print("\n" + "="*50)
    print("分析完成！")
    print("="*50)
    
    return analyzer

if __name__ == "__main__":
    analyzer = main()
